<template>
  <div id="app">
    <a-layout class="layout">
      <!-- 顶部导航 -->
      <a-layout-header class="header">
        <div class="header-content" data-tauri-drag-region>
          <div class="logo">
            <h3>概算管理系统</h3>
          </div>
          <div class="header-center" data-tauri-drag-region>
            <!-- 可拖动区域 -->
          </div>
          <div class="header-right">
            <div class="actions">
              <a-space>
                <a-button type="primary" @click="newEstimate">
                  <template #icon><PlusOutlined /></template>
                  新建概算
                </a-button>
                <a-button @click="importData">
                  <template #icon><ImportOutlined /></template>
                  导入数据
                </a-button>
                <a-button @click="exportData">
                  <template #icon><ExportOutlined /></template>
                  导出数据
                </a-button>
              </a-space>
            </div>
            <div class="window-controls">
              <a-button
                type="text"
                size="small"
                class="window-control-btn"
                @click="minimizeWindow"
                title="最小化"
              >
                <template #icon><MinusOutlined /></template>
              </a-button>
              <a-button
                type="text"
                size="small"
                class="window-control-btn"
                @click="toggleMaximize"
                :title="isMaximized ? '还原' : '最大化'"
              >
                <template #icon>
                  <BorderOutlined v-if="!isMaximized" />
                  <ShrinkOutlined v-else />
                </template>
              </a-button>
              <a-button
                type="text"
                size="small"
                class="window-control-btn close-btn"
                @click="closeWindow"
                title="关闭"
              >
                <template #icon><CloseOutlined /></template>
              </a-button>
            </div>
          </div>
        </div>
      </a-layout-header>

      <!-- 主内容区 -->
      <a-layout-content class="content">
        <div class="content-wrapper">
          <!-- 工具栏 -->
          <a-card class="toolbar-card" :bordered="false">
            <a-row :gutter="16" align="middle">
              <a-col :span="8">
                <a-input-search
                  v-model:value="searchText"
                  placeholder="搜索概算项目..."
                  @search="handleSearch"
                />
              </a-col>
              <a-col :span="4">
                <a-select
                  v-model:value="statusFilter"
                  placeholder="状态筛选"
                  style="width: 100%"
                  @change="handleFilter"
                >
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option value="draft">草稿</a-select-option>
                  <a-select-option value="reviewing">审核中</a-select-option>
                  <a-select-option value="approved">已批准</a-select-option>
                </a-select>
              </a-col>
              <a-col :span="4">
                <a-date-picker
                  v-model:value="dateFilter"
                  placeholder="选择日期"
                  style="width: 100%"
                  @change="handleFilter"
                />
              </a-col>
              <a-col :span="8" style="text-align: right">
                <a-statistic
                  title="概算总金额"
                  :value="totalAmount"
                  :precision="2"
                  suffix="万元"
                  :value-style="{ color: '#1890ff' }"
                />
              </a-col>
            </a-row>
          </a-card>

          <!-- 概算列表 -->
          <a-card title="概算项目列表" class="table-card">
            <a-table
              :columns="columns"
              :data-source="filteredData"
              :pagination="pagination"
              :loading="loading"
              row-key="id"
              @change="handleTableChange"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'status'">
                  <a-tag :color="getStatusColor(record.status)">
                    {{ getStatusText(record.status) }}
                  </a-tag>
                </template>
                <template v-else-if="column.key === 'amount'">
                  <span style="color: #1890ff; font-weight: 600;">
                    {{ record.amount.toFixed(2) }} 万元
                  </span>
                </template>
                <template v-else-if="column.key === 'action'">
                  <a-space>
                    <a-button type="link" size="small" @click="viewDetail(record)">
                      查看
                    </a-button>
                    <a-button type="link" size="small" @click="editRecord(record)">
                      编辑
                    </a-button>
                    <a-button type="link" size="small" danger @click="deleteRecord(record)">
                      删除
                    </a-button>
                  </a-space>
                </template>
              </template>
            </a-table>
          </a-card>
        </div>
      </a-layout-content>
    </a-layout>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  ImportOutlined,
  ExportOutlined,
  MinusOutlined,
  BorderOutlined,
  ShrinkOutlined,
  CloseOutlined
} from '@ant-design/icons-vue'
import { getCurrentWindow } from '@tauri-apps/api/window'

// 窗口状态
const isMaximized = ref(false)
const currentWindow = ref(null)

// 响应式数据
const loading = ref(false)
const searchText = ref('')
const statusFilter = ref('')
const dateFilter = ref(null)

// 表格列定义
const columns = [
  {
    title: '项目名称',
    dataIndex: 'name',
    key: 'name',
    width: 200,
  },
  {
    title: '项目编号',
    dataIndex: 'code',
    key: 'code',
    width: 120,
  },
  {
    title: '概算金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 120,
    sorter: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 120,
  },
  {
    title: '负责人',
    dataIndex: 'manager',
    key: 'manager',
    width: 100,
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
  },
]

// 模拟数据
const estimateData = ref([
  {
    id: 1,
    name: '办公楼建设项目概算',
    code: 'GS2024001',
    amount: 2500.50,
    status: 'approved',
    createTime: '2024-01-15',
    manager: '张三'
  },
  {
    id: 2,
    name: '道路改造工程概算',
    code: 'GS2024002',
    amount: 1800.30,
    status: 'reviewing',
    createTime: '2024-01-20',
    manager: '李四'
  },
  {
    id: 3,
    name: '园林绿化项目概算',
    code: 'GS2024003',
    amount: 950.80,
    status: 'draft',
    createTime: '2024-01-25',
    manager: '王五'
  },
  {
    id: 4,
    name: '停车场建设概算',
    code: 'GS2024004',
    amount: 680.20,
    status: 'approved',
    createTime: '2024-02-01',
    manager: '赵六'
  }
])

// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 计算属性
const filteredData = computed(() => {
  let data = estimateData.value

  // 搜索过滤
  if (searchText.value) {
    data = data.filter(item =>
      item.name.includes(searchText.value) ||
      item.code.includes(searchText.value) ||
      item.manager.includes(searchText.value)
    )
  }

  // 状态过滤
  if (statusFilter.value) {
    data = data.filter(item => item.status === statusFilter.value)
  }

  pagination.value.total = data.length
  return data
})

const totalAmount = computed(() => {
  return filteredData.value.reduce((sum, item) => sum + item.amount, 0)
})

// 方法
const getStatusColor = (status) => {
  const colors = {
    draft: 'orange',
    reviewing: 'blue',
    approved: 'green'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    draft: '草稿',
    reviewing: '审核中',
    approved: '已批准'
  }
  return texts[status] || '未知'
}

const newEstimate = () => {
  message.info('新建概算功能开发中...')
}

const importData = () => {
  message.info('导入数据功能开发中...')
}

const exportData = () => {
  message.success('数据导出成功！')
}

const handleSearch = () => {
  console.log('搜索:', searchText.value)
}

const handleFilter = () => {
  console.log('筛选条件变更')
}

const handleTableChange = (pag, filters, sorter) => {
  pagination.value = pag
  console.log('表格变更:', pag, filters, sorter)
}

const viewDetail = (record) => {
  message.info(`查看概算详情: ${record.name}`)
}

const editRecord = (record) => {
  message.info(`编辑概算: ${record.name}`)
}

const deleteRecord = (record) => {
  message.warning(`删除概算: ${record.name}`)
}

// 窗口控制方法
const minimizeWindow = async () => {
  try {
    if (currentWindow.value) {
      await currentWindow.value.minimize()
      console.log('窗口已最小化')
    }
  } catch (error) {
    console.error('最小化窗口失败:', error)
    message.error('最小化失败')
  }
}

const toggleMaximize = async () => {
  try {
    if (currentWindow.value) {
      if (isMaximized.value) {
        await currentWindow.value.unmaximize()
        isMaximized.value = false
        console.log('概算窗口已还原')
      } else {
        await currentWindow.value.maximize()
        isMaximized.value = true
        console.log('概算窗口已最大化')
      }
    } else {
      console.error('概算窗口对象未初始化')
      message.error('窗口对象未初始化')
    }
  } catch (error) {
    console.error('切换最大化状态失败:', error)
    message.error('窗口操作失败: ' + error)
  }
}

const closeWindow = async () => {
  try {
    if (currentWindow.value) {
      await currentWindow.value.close()
      console.log('概算窗口已关闭')
    } else {
      console.error('概算窗口对象未初始化')
      message.error('窗口对象未初始化')
    }
  } catch (error) {
    console.error('关闭窗口失败:', error)
    message.error('关闭失败: ' + error)
  }
}

onMounted(async () => {
  console.log('概算管理系统已加载')
  pagination.value.total = estimateData.value.length

  // 初始化窗口控制
  try {
    // 等待一下确保 Tauri 环境准备好
    await new Promise(resolve => setTimeout(resolve, 100))

    currentWindow.value = getCurrentWindow()
    console.log('概算窗口对象已初始化:', currentWindow.value)

    // 获取初始窗口状态
    isMaximized.value = await currentWindow.value.isMaximized()
    console.log('概算窗口初始最大化状态:', isMaximized.value)

    // 监听窗口状态变化
    await currentWindow.value.onResized(() => {
      currentWindow.value.isMaximized().then(maximized => {
        isMaximized.value = maximized
        console.log('概算窗口状态更新:', maximized ? '最大化' : '正常')
      })
    })

  } catch (error) {
    console.error('初始化概算窗口状态失败:', error)
    message.error('窗口初始化失败: ' + error)
  }
})
</script>

<style scoped>
.layout {
  height: 100vh;
}

.header {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  padding: 0;
  border-bottom: none;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  width: 100%;
  padding: 0 24px;
}

.header-center {
  flex: 1;
  height: 100%;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.logo h3 {
  margin: 0;
  color: white;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.content {
  background: #f0f2f5;
  padding: 24px;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.toolbar-card {
  margin-bottom: 16px;
}

.table-card {
  background: white;
}

/* 窗口控制按钮样式 */
.window-controls {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-left: 16px;
}

.window-control-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.window-control-btn {
  color: white;
}

.window-control-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.window-control-btn.close-btn:hover {
  background-color: #ff4d4f;
  color: white;
}

/* 拖动区域样式 */
[data-tauri-drag-region] {
  -webkit-app-region: drag;
}

/* 确保按钮不被拖动 */
.window-controls,
.actions,
.logo {
  -webkit-app-region: no-drag;
}
</style>
